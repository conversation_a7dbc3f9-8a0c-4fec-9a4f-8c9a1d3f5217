"""
一周热门话题发现系统 - 专为带货达人寻找优化
基于话题聚类和商业潜力评估，专注于可商业化推广的热门话题
"""

import requests
import json
import re
import time
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from typing import List, Dict, Any, Tuple, Set
import hashlib
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class InfluencerTopicsDetector:
    """带货达人话题发现器"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://newsapi.org/v2"
        self.top_sources = [
            'bbc-news', 'cnn', 'reuters', 'associated-press', 
            'the-washington-post', 'bloomberg', 'techcrunch',
            'the-guardian-uk', 'abc-news', 'financial-times'
        ]
        
        # 商业化关键词 - 适合带货推广的话题
        self.commercial_keywords = {
            # 科技产品
            'tech': ['iphone', 'android', 'smartphone', 'laptop', 'gaming', 'headphones', 'smartwatch', 'tablet', 'camera', 'drone'],
            # 健康美容
            'beauty': ['skincare', 'makeup', 'beauty', 'cosmetics', 'wellness', 'fitness', 'diet', 'supplement', 'health'],
            # 时尚生活
            'fashion': ['fashion', 'clothing', 'shoes', 'accessories', 'lifestyle', 'home', 'decor', 'kitchen', 'cooking'],
            # 娱乐消费
            'entertainment': ['movie', 'game', 'music', 'streaming', 'netflix', 'amazon', 'shopping', 'book', 'travel'],
            # 汽车交通
            'automotive': ['car', 'electric', 'tesla', 'vehicle', 'bike', 'motorcycle', 'automotive'],
            # 投资理财
            'finance': ['investment', 'crypto', 'bitcoin', 'stock', 'trading', 'money', 'finance', 'economy']
        }
        
        # 排除词 - 不适合带货的话题
        self.excluded_keywords = {
            'politics', 'war', 'death', 'murder', 'terrorism', 'disaster', 'accident', 
            'crisis', 'protest', 'violence', 'court', 'legal', 'lawsuit', 'investigation'
        }
        
        # 停用词列表
        self.stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during',
            'before', 'after', 'above', 'below', 'over', 'under', 'again', 'further',
            'then', 'once', 'here', 'there', 'when', 'where', 'why', 'how', 'all',
            'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such',
            'no', 'nor', 'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very',
            'can', 'will', 'just', 'should', 'now', 'says', 'said', 'new', 'news'
        }
        
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': 'InfluencerTopicsDetector/1.0'})

    def get_influencer_friendly_topics(self) -> Dict[str, Any]:
        """获取适合带货达人的热门话题"""
        now = datetime.now()
        one_week_ago = now - timedelta(days=7)
        
        logger.info("🔍 搜索适合带货推广的热门话题...")
        
        try:
            # 收集新闻数据
            news_collection = self.collect_weekly_news(one_week_ago, now)
            logger.info(f"📊 收集到 {len(news_collection)} 篇文章")
            
            # 过滤商业化话题
            commercial_articles = self.filter_commercial_articles(news_collection)
            logger.info(f"💼 筛选出 {len(commercial_articles)} 篇商业相关文章")
            
            # 话题聚类和提取
            topics = self.extract_and_cluster_topics(commercial_articles)
            logger.info(f"🏷️ 识别出 {len(topics)} 个商业话题")
            
            # 商业潜力评分
            scored_topics = self.score_commercial_potential(topics)
            
            # 话题多样性保证
            diverse_topics = self.ensure_topic_diversity(scored_topics)
            
            # 生成达人搜索建议
            final_topics = self.generate_influencer_search_suggestions(diverse_topics)
            
            # 最终排序，返回TOP10
            top_topics = sorted(final_topics, key=lambda x: x['commercial_score'], reverse=True)[:10]

            return {
                'timestamp': now.isoformat(),
                'week_range': {
                    'from': one_week_ago.strftime('%Y-%m-%d'),
                    'to': now.strftime('%Y-%m-%d')
                },
                'total_articles_analyzed': len(news_collection),
                'commercial_articles_found': len(commercial_articles),
                'influencer_friendly_topics': len(top_topics),
                'top_topics_for_influencers': top_topics
            }

        except Exception as error:
            logger.error(f"话题检测失败: {error}")
            return {'error': str(error), 'top_topics_for_influencers': []}

    def filter_commercial_articles(self, articles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """过滤出有商业潜力的文章"""
        commercial_articles = []
        
        for article in articles:
            if not article:
                continue
            title = (article.get('title') or '').lower()
            description = (article.get('description') or '').lower()
            content = f"{title} {description}"
            
            # 检查是否包含排除关键词
            has_excluded = any(keyword in content for keyword in self.excluded_keywords)
            if has_excluded:
                continue
            
            # 检查是否包含商业关键词
            commercial_score = 0
            matched_categories = []
            
            for category, keywords in self.commercial_keywords.items():
                category_matches = sum(1 for keyword in keywords if keyword in content)
                if category_matches > 0:
                    commercial_score += category_matches
                    matched_categories.append(category)
            
            # 排除纯新闻简报
            if 'bulletin' in content and 'minute' in content:
                continue
                
            # 只保留有商业潜力的文章，或者包含常见商业品牌的文章
            brand_keywords = ['amazon', 'apple', 'google', 'tesla', 'netflix', 'microsoft', 'samsung', 'sony', 'meta', 'nvidia']
            has_brand = any(brand in content for brand in brand_keywords)
            
            if commercial_score > 0 or has_brand:
                if commercial_score == 0 and has_brand:
                    commercial_score = 1  # 给品牌文章基础分数
                article['commercial_score'] = commercial_score
                article['commercial_categories'] = matched_categories if matched_categories else ['tech']
                commercial_articles.append(article)
        
        return commercial_articles

    def light_filter_articles(self, articles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """轻度过滤，只排除明显不适合的内容，保留更多新闻"""
        filtered_articles = []
        
        # 只排除最明显不适合的内容
        strong_exclude_keywords = {
            'murder', 'killed', 'death', 'died', 'shooting', 'stabbing', 
            'terrorism', 'war', 'violence', 'rape', 'suicide', 'crime',
            'accident', 'crash', 'disaster', 'tragedy'
        }
        
        for article in articles:
            if not article:
                continue
            
            title = (article.get('title') or '').lower()
            description = (article.get('description') or '').lower()
            content = f"{title} {description}"
            
            # 排除明显负面或不适合的内容
            has_strong_negative = any(keyword in content for keyword in strong_exclude_keywords)
            
            # 排除空标题或无效文章
            if not title or title in ['no title', '', '[removed]']:
                continue
                
            # 排除纯新闻简报
            if 'news bulletin' in content or 'minute by minute' in content:
                continue
            
            if not has_strong_negative:
                filtered_articles.append(article)
        
        return filtered_articles

    def calculate_hotness_score(self, article: Dict[str, Any]) -> float:
        """计算新闻热门度评分"""
        score = 0.0
        
        title = (article.get('title') or '').lower()
        description = (article.get('description') or '').lower()
        content = f"{title} {description}"
        source = article.get('source', {})
        source_name = source.get('name', '').lower()
        
        # 1. 来源权威性评分 (30%)
        authority_sources = {
            'bbc': 10, 'cnn': 10, 'reuters': 10, 'bloomberg': 9,
            'techcrunch': 8, 'the guardian': 8, 'washington post': 8,
            'wall street journal': 9, 'financial times': 9, 'ap news': 8,
            'abc news': 7, 'nbc news': 7, 'cbs news': 7, 'fox news': 6
        }
        
        for source_key, source_score in authority_sources.items():
            if source_key in source_name:
                score += source_score * 3
                break
        else:
            score += 15  # 基础分数给其他来源
        
        # 2. 发布时间新鲜度 (25%)
        try:
            pub_date = datetime.fromisoformat(article['publishedAt'].replace('Z', '+00:00'))
            hours_ago = (datetime.now() - pub_date.replace(tzinfo=None)).total_seconds() / 3600
            
            if hours_ago <= 6:
                score += 25  # 6小时内
            elif hours_ago <= 24:
                score += 20  # 24小时内
            elif hours_ago <= 48:
                score += 15  # 48小时内
            else:
                score += max(0, 15 - hours_ago / 24)  # 递减
        except:
            score += 10  # 无法解析时间时的默认分数
        
        # 3. 标题吸引力 (20%)
        attention_words = [
            'breaking', 'exclusive', 'major', 'huge', 'massive', 'dramatic',
            'shocking', 'surprising', 'unprecedented', 'record', 'first time',
            'milestone', 'breakthrough', 'revolutionary', 'game-changing'
        ]
        
        title_score = sum(2 for word in attention_words if word in content)
        score += min(title_score, 20)
        
        # 4. 热门关键词/话题 (15%)
        trending_keywords = [
            'ai', 'artificial intelligence', 'chatgpt', 'openai', 'microsoft',
            'apple', 'iphone', 'google', 'meta', 'twitter', 'tesla', 'elon musk',
            'bitcoin', 'crypto', 'nft', 'streaming', 'netflix', 'amazon',
            'climate', 'election', 'economy', 'inflation', 'stock market'
        ]
        
        trend_score = sum(1.5 for keyword in trending_keywords if keyword in content)
        score += min(trend_score, 15)
        
        # 5. 内容质量指标 (10%)
        # 标题长度适中
        title_length = len(title)
        if 30 <= title_length <= 100:
            score += 5
        
        # 有描述内容
        if description and len(description) > 50:
            score += 5
        
        return round(score, 1)

    def score_commercial_potential(self, topics: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """评估话题的商业推广潜力"""
        scored_topics = []
        
        for topic in topics:
            score = 0
            
            # 1. 商业关键词密度 (40%)
            commercial_keywords_count = 0
            for article in topic['articles']:
                commercial_keywords_count += article.get('commercial_score', 0)
            
            keyword_density_score = min(commercial_keywords_count, 20) * 2
            score += keyword_density_score
            
            # 2. 话题持续时间和热度 (25%)
            articles_count = len(topic['articles'])
            heat_score = min(articles_count * 3, 30)
            score += heat_score
            
            # 3. 媒体覆盖多样性 (20%)
            unique_sources = len(topic['sources'])
            source_score = min(unique_sources * 4, 20)
            score += source_score
            
            # 4. 新鲜度 (10%)
            latest_date = max(datetime.fromisoformat(article['publishedAt'].replace('Z', '+00:00'))
                            for article in topic['articles'])
            hours_ago = (datetime.now() - latest_date.replace(tzinfo=None)).total_seconds() / 3600
            freshness_score = max(0, 10 - hours_ago / 24) 
            score += freshness_score
            
            # 5. 商业类别多样性 (5%)
            categories = set()
            for article in topic['articles']:
                categories.update(article.get('commercial_categories', []))
            category_bonus = len(categories) * 1
            score += category_bonus
            
            scored_topic = {
                **topic,
                'commercial_score': score,
                'commercial_categories': list(categories),
                'scoring_details': {
                    'keyword_density_score': keyword_density_score,
                    'heat_score': heat_score,
                    'source_score': source_score,
                    'freshness_score': freshness_score,
                    'category_bonus': category_bonus
                }
            }
            scored_topics.append(scored_topic)
        
        return scored_topics

    def generate_influencer_search_suggestions(self, topics: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """为每个话题生成达人搜索建议"""
        enhanced_topics = []
        
        for topic in topics:
            # 生成搜索关键词
            search_keywords = self.generate_search_keywords(topic)
            
            # 推荐平台策略
            platform_strategy = self.suggest_platform_strategy(topic)
            
            # 生成达人类型建议
            influencer_types = self.suggest_influencer_types(topic)
            
            enhanced_topic = {
                **topic,
                'influencer_search': {
                    'primary_keywords': search_keywords[:3],
                    'secondary_keywords': search_keywords[3:6],
                    'platform_strategy': platform_strategy,
                    'recommended_influencer_types': influencer_types,
                    'search_templates': {
                        'youtube': f"{search_keywords[0]} review",
                        'tiktok': f"#{search_keywords[0]} #review",
                        'general': f"{search_keywords[0]} influencer"
                    }
                }
            }
            enhanced_topics.append(enhanced_topic)
        
        return enhanced_topics

    def generate_search_keywords(self, topic: Dict[str, Any]) -> List[str]:
        """生成搜索关键词"""
        keywords = []
        
        # 从话题关键词中选择
        topic_keywords = topic.get('topic_keywords', [])
        keywords.extend(topic_keywords[:3])
        
        # 从商业类别中添加
        commercial_categories = topic.get('commercial_categories', [])
        for category in commercial_categories:
            if category == 'tech':
                keywords.extend(['tech review', 'gadget', 'unboxing'])
            elif category == 'beauty':
                keywords.extend(['beauty', 'skincare', 'makeup'])
            elif category == 'fashion':
                keywords.extend(['fashion', 'style', 'outfit'])
            elif category == 'entertainment':
                keywords.extend(['entertainment', 'lifestyle', 'review'])
            elif category == 'automotive':
                keywords.extend(['car review', 'automotive', 'vehicle'])
            elif category == 'finance':
                keywords.extend(['finance', 'investment', 'money'])
        
        # 去重并返回
        return list(dict.fromkeys(keywords))[:6]

    def suggest_platform_strategy(self, topic: Dict[str, Any]) -> Dict[str, str]:
        """建议平台策略"""
        categories = topic.get('commercial_categories', [])
        
        if 'tech' in categories:
            return {
                'youtube': '适合深度评测和开箱视频',
                'tiktok': '适合快速上手和功能展示',
                'focus': 'youtube'
            }
        elif 'beauty' in categories:
            return {
                'youtube': '适合化妆教程和产品测评',
                'tiktok': '适合快速妆容和前后对比',
                'focus': 'tiktok'
            }
        elif 'fashion' in categories:
            return {
                'youtube': '适合穿搭教程和品牌介绍',
                'tiktok': '适合快速换装和潮流展示',
                'focus': 'tiktok'
            }
        else:
            return {
                'youtube': '适合详细介绍和深度分析',
                'tiktok': '适合快速展示和趋势跟进',
                'focus': 'both'
            }

    def suggest_influencer_types(self, topic: Dict[str, Any]) -> List[str]:
        """建议达人类型"""
        categories = topic.get('commercial_categories', [])
        types = []
        
        if 'tech' in categories:
            types.extend(['科技博主', '数码评测师', '极客达人'])
        if 'beauty' in categories:
            types.extend(['美妆博主', '护肤达人', '美容师'])
        if 'fashion' in categories:
            types.extend(['时尚博主', '穿搭达人', '潮流KOL'])
        if 'entertainment' in categories:
            types.extend(['生活方式博主', '娱乐达人', '综合KOL'])
        if 'automotive' in categories:
            types.extend(['汽车博主', '车评人', '改装达人'])
        if 'finance' in categories:
            types.extend(['理财博主', '投资分析师', '财经KOL'])
        
        return types[:3] if types else ['综合达人', '生活博主', '产品评测师']

    def collect_weekly_news(self, from_date: datetime, to_date: datetime) -> List[Dict[str, Any]]:
        """多策略收集一周新闻"""
        from_str = from_date.strftime('%Y-%m-%d')
        to_str = to_date.strftime('%Y-%m-%d')
        
        all_news = []
        
        # 使用线程池并发获取新闻
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [
                # 策略1: 商业和科技类别新闻
                executor.submit(self.get_category_news, 
                    ['business', 'technology', 'health', 'entertainment']),
                
                # 策略2: 权威媒体新闻
                executor.submit(self.get_authority_media_news, from_str, to_str),
                
                # 策略3: 商业化关键词新闻
                executor.submit(self.get_commercial_keyword_news, from_str, to_str)
            ]
            
            for future in as_completed(futures):
                try:
                    result = future.result()
                    if isinstance(result, list):
                        all_news.extend(result)
                    elif isinstance(result, dict) and 'articles' in result:
                        all_news.extend(result['articles'])
                except Exception as e:
                    logger.warning(f"获取新闻时出错: {e}")
        
        # 过滤和去重
        valid_news = [article for article in all_news 
                     if article and article.get('title') and article.get('publishedAt')]
        
        unique_news = self.deduplicate_by_url(valid_news)
        return unique_news

    def get_commercial_keyword_news(self, from_date: str, to_date: str) -> List[Dict[str, Any]]:
        """获取商业化关键词新闻"""
        commercial_keywords = [
            'iphone', 'tesla', 'netflix', 'amazon', 'google', 
            'apple', 'samsung', 'gaming', 'crypto', 'bitcoin'
        ]

        all_articles = []
        
        for keyword in commercial_keywords[:5]:
            try:
                result = self.get_news_with_params({
                    'q': keyword,
                    'from': from_date,
                    'to': to_date,
                    'sortBy': 'popularity',
                    'pageSize': 20
                })
                all_articles.extend(result.get('articles', []))
                time.sleep(0.1)  # 避免请求过快
                
            except Exception as e:
                logger.warning(f"获取关键词 {keyword} 新闻失败: {e}")

        return all_articles

    def extract_and_cluster_topics(self, articles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """话题提取和聚类"""
        logger.info("🧠 开始话题聚类分析...")
        
        # 1. 提取每篇文章的关键词
        articles_with_keywords = []
        for article in articles:
            text = f"{article.get('title', '')} {article.get('description', '')}"
            keywords = self.extract_keywords(text)
            normalized_title = self.normalize_title(article.get('title', ''))
            
            articles_with_keywords.append({
                **article,
                'keywords': keywords,
                'normalized_title': normalized_title
            })

        # 2. 基于关键词相似度进行聚类
        clusters = self.perform_topic_clustering(articles_with_keywords)
        
        # 3. 为每个聚类生成话题描述
        topics = []
        for cluster in clusters:
            if len(cluster) >= 1:  # 降低话题门槛
                topic = {
                    'topic_id': self.generate_topic_id(cluster),
                    'topic_title': self.generate_topic_title(cluster),
                    'topic_keywords': self.extract_topic_keywords(cluster),
                    'articles_count': len(cluster),
                    'articles': cluster,
                    'date_range': self.get_cluster_date_range(cluster),
                    'sources': self.get_cluster_sources(cluster)
                }
                topics.append(topic)

        return topics

    def extract_keywords(self, text: str) -> Dict[str, int]:
        """关键词提取"""
        # 清理和分词
        text = re.sub(r'[^\w\s]', ' ', text.lower())
        words = [word for word in text.split() 
                if len(word) > 3 and word not in self.stop_words]
        
        # 统计词频
        return dict(Counter(words))

    def normalize_title(self, title: str) -> str:
        """标题标准化"""
        return re.sub(r'\s+', ' ', re.sub(r'[^\w\s]', ' ', title.lower())).strip()

    def perform_topic_clustering(self, articles: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """话题聚类算法"""
        clusters = []
        processed = set()

        for i, article in enumerate(articles):
            if i in processed:
                continue

            cluster = [article]
            processed.add(i)

            # 寻找相似文章
            for j, other_article in enumerate(articles):
                if j == i or j in processed:
                    continue

                similarity = self.calculate_topic_similarity(article, other_article)
                
                if similarity > 0.3:  # 降低相似度阈值，更容易聚类
                    cluster.append(other_article)
                    processed.add(j)

            if len(cluster) >= 1:  # 降低最小聚类大小
                clusters.append(cluster)

        return sorted(clusters, key=len, reverse=True)

    def calculate_topic_similarity(self, article1: Dict[str, Any], article2: Dict[str, Any]) -> float:
        """话题相似度计算"""
        # 1. 标题相似度
        title_sim = self.calculate_text_similarity(
            article1['normalized_title'],
            article2['normalized_title']
        )

        # 2. 关键词重叠度
        keywords1 = set(article1['keywords'].keys())
        keywords2 = set(article2['keywords'].keys())
        
        if not keywords1 or not keywords2:
            keyword_sim = 0
        else:
            intersection = keywords1 & keywords2
            union = keywords1 | keywords2
            keyword_sim = len(intersection) / len(union)

        # 综合相似度
        return title_sim * 0.6 + keyword_sim * 0.4

    def calculate_text_similarity(self, text1: str, text2: str) -> float:
        """文本相似度计算（Jaccard相似度）"""
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        if not words1 or not words2:
            return 0
        
        intersection = words1 & words2
        union = words1 | words2
        
        return len(intersection) / len(union)

    def generate_topic_title(self, cluster: List[Dict[str, Any]]) -> str:
        """话题标题生成"""
        # 找到最频繁的关键词组合
        all_keywords = defaultdict(int)
        
        for article in cluster:
            for keyword, count in article['keywords'].items():
                all_keywords[keyword] += count

        # 获取最热门的关键词
        top_keywords = [keyword for keyword, _ in 
                       sorted(all_keywords.items(), key=lambda x: x[1], reverse=True)[:3]]

        # 尝试从标题中提取最有代表性的短语
        representative_title = self.find_most_representative_title(cluster)
        
        return representative_title or ' '.join(top_keywords)

    def find_most_representative_title(self, cluster: List[Dict[str, Any]]) -> str:
        """寻找最有代表性的标题"""
        if not cluster:
            return ""
        
        # 如果没有共同词汇，返回最短的标题
        shortest_title = min(cluster, key=lambda x: len(x.get('title', '')))
        title = shortest_title.get('title', '')
        return title.split(':', 1)[0].split('-', 1)[0].strip()

    def ensure_topic_diversity(self, topics: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """确保话题多样性"""
        diverse_topics = []
        used_keywords = set()

        # 按商业评分排序
        sorted_topics = sorted(topics, key=lambda x: x['commercial_score'], reverse=True)
        
        for topic in sorted_topics:
            topic_keywords = topic['topic_keywords'][:3]
            has_overlap = any(keyword in used_keywords for keyword in topic_keywords)

            if not has_overlap or len(diverse_topics) < 5:
                diverse_topics.append(topic)
                used_keywords.update(topic_keywords)

        return diverse_topics

    def extract_topic_keywords(self, cluster: List[Dict[str, Any]]) -> List[str]:
        """提取话题关键词"""
        all_keywords = defaultdict(int)
        
        for article in cluster:
            for keyword, count in article['keywords'].items():
                all_keywords[keyword] += count

        return [keyword for keyword, _ in 
                sorted(all_keywords.items(), key=lambda x: x[1], reverse=True)[:5]]

    def get_cluster_date_range(self, cluster: List[Dict[str, Any]]) -> Dict[str, datetime]:
        """获取聚类的日期范围"""
        dates = []
        for article in cluster:
            try:
                date = datetime.fromisoformat(article['publishedAt'].replace('Z', '+00:00'))
                dates.append(date.replace(tzinfo=None))
            except:
                continue
        
        if not dates:
            now = datetime.now()
            return {'start': now, 'end': now}
        
        return {
            'start': min(dates),
            'end': max(dates)
        }

    def get_cluster_sources(self, cluster: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """获取聚类的媒体来源"""
        sources = {}
        for article in cluster:
            source = article.get('source')
            if source:
                source_id = source.get('id') or source.get('name', '')
                if source_id:
                    sources[source_id] = source
        
        return list(sources.values())

    def deduplicate_by_url(self, articles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """根据URL去重"""
        seen = set()
        unique_articles = []
        
        for article in articles:
            url = article.get('url', '')
            if url and url not in seen:
                seen.add(url)
                unique_articles.append(article)
        
        return unique_articles

    def generate_topic_id(self, cluster: List[Dict[str, Any]]) -> str:
        """生成话题ID"""
        if not cluster:
            return f"topic_{int(time.time())}"
        
        first_article = cluster[0]
        keywords = list(first_article.get('keywords', {}).keys())[:2]
        keywords_str = '_'.join(keywords) if keywords else 'unknown'
        return f"topic_{int(time.time())}_{keywords_str}".lower()

    # API调用方法
    def get_news_with_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """使用参数获取新闻"""
        url = f"{self.base_url}/everything"
        params['apiKey'] = self.api_key
        params['language'] = 'en'
        
        try:
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"API调用失败: {e}")
            return {'articles': []}

    def get_category_news(self, categories: List[str]) -> List[Dict[str, Any]]:
        """获取分类新闻"""
        all_articles = []
        
        for category in categories:
            try:
                url = f"{self.base_url}/top-headlines"
                params = {
                    'category': category,
                    'pageSize': 20,
                    'apiKey': self.api_key
                }
                
                response = self.session.get(url, params=params, timeout=30)
                if response.ok:
                    data = response.json()
                    all_articles.extend(data.get('articles', []))
                    
                time.sleep(0.1)  # 避免请求过快
                
            except Exception as e:
                logger.warning(f"获取 {category} 类别新闻失败: {e}")
        
        return all_articles

    def get_authority_media_news(self, from_date: str, to_date: str) -> List[Dict[str, Any]]:
        """获取权威媒体新闻"""
        sources = ','.join(self.top_sources[:5])
        
        try:
            result = self.get_news_with_params({
                'sources': sources,
                'from': from_date,
                'to': to_date,
                'sortBy': 'publishedAt',
                'pageSize': 100
            })
            return result.get('articles', [])
        except Exception as e:
            logger.warning(f"获取权威媒体新闻失败: {e}")
            return []

    def calculate_commercial_score(self, article: Dict[str, Any]) -> float:
        """计算文章的商业潜力分数，用于带货达人搜索"""
        title = (article.get('title') or '').lower()
        description = (article.get('description') or '').lower()
        content = f"{title} {description}"

        # 品牌、产品、消费场景、负面词典
        brand_words = [
            'apple', 'iphone', 'ipad', 'mac', 'google', 'pixel', 'samsung', 'galaxy',
            'sony', 'playstation', 'microsoft', 'xbox', 'nintendo', 'tesla', 'netflix',
            'amazon', 'nike', 'adidas', 'louis vuitton', 'gucci', 'huawei', 'xiaomi',
            'oppo', 'vivo', 'oneplus', 'lenovo', 'hp', 'dell', 'rog', 'asus', 'razer'
        ]
        product_words = [
            'smartphone', 'laptop', 'tablet', 'headphones', 'earbuds', 'camera',
            'drone', 'smartwatch', 'vr', 'ar', 'skincare', 'makeup', 'lipstick',
            'foundation', 'serum', 'sneakers', 'handbag', 'backpack', 'gaming',
            'monitor', 'router', 'electric car', 'ev', 'subscription', 'streaming'
        ]
        scene_words = [
            'review', 'unboxing', 'hands-on', 'discount', 'deal', 'sale', 'preorder',
            'launch', 'announcement', 'upgrade', 'test drive', 'haul', 'tutorial'
        ]
        negative_words = ['recall', 'lawsuit', 'hacked', 'scandal']

        score = 0.0
        score += 3 * sum(1 for w in brand_words if w in content)
        score += 2 * sum(1 for w in product_words if w in content)
        score += 1 * sum(1 for w in scene_words if w in content)
        if any(neg in content for neg in negative_words):
            score -= 5
        return max(score, 0.0)

    def extract_search_keywords(self, article: Dict[str, Any], max_k: int = 3) -> List[str]:
        """抽取用于搜索达人的关键词，优先品牌/产品词"""
        title_text = (article.get('title') or '').lower()
        description_text = (article.get('description') or '').lower()
        content = f"{title_text} {description_text}"

        # 与 calculate_commercial_score 使用相同词库
        brand_words = [
            'apple', 'iphone', 'ipad', 'mac', 'google', 'pixel', 'samsung', 'galaxy',
            'sony', 'playstation', 'microsoft', 'xbox', 'nintendo', 'tesla', 'netflix',
            'amazon', 'nike', 'adidas', 'louis vuitton', 'gucci', 'huawei', 'xiaomi',
            'oppo', 'vivo', 'oneplus', 'lenovo', 'hp', 'dell', 'rog', 'asus', 'razer'
        ]
        product_words = [
            'smartphone', 'laptop', 'tablet', 'headphones', 'earbuds', 'camera',
            'drone', 'smartwatch', 'vr', 'ar', 'skincare', 'makeup', 'lipstick',
            'foundation', 'serum', 'sneakers', 'handbag', 'backpack', 'gaming',
            'monitor', 'router', 'electric car', 'ev', 'subscription', 'streaming'
        ]

        keywords: List[str] = []
        for w in brand_words + product_words:
            if w in content and w not in keywords:
                keywords.append(w)
            if len(keywords) >= max_k:
                break

        # 如果不足 max_k，再补充标题中的高频词
        if len(keywords) < max_k:
            words = [w for w in re.sub(r'[^\w\s]', ' ', title_text).split() if len(w) > 3 and w not in self.stop_words]
            for w in words:
                if w not in keywords:
                    keywords.append(w)
                if len(keywords) >= max_k:
                    break
        return keywords[:max_k]


# 使用示例和演示函数
def get_commercial_news_titles():
    """获取100个最热门的新闻标题（适合了解趋势和寻找达人）"""
    api_key = "6975e8c0e3904a499b90c2e8002b6052"
    detector = InfluencerTopicsDetector(api_key)
    
    try:
        print("🔍 正在收集最热门的新闻标题...\n")
        
        # 收集新闻数据
        now = datetime.now()
        one_week_ago = now - timedelta(days=7)
        news_collection = detector.collect_weekly_news(one_week_ago, now)
        print(f"📊 收集到 {len(news_collection)} 篇文章")
        
        # 轻度过滤，只排除明显不适合的内容
        filtered_articles = detector.light_filter_articles(news_collection)
        print(f"🔥 筛选出 {len(filtered_articles)} 篇热门文章")
        
        # 基于热门程度评分
        scored_articles = []
        for article in filtered_articles:
            # 热门度评分算法
            hot_score = detector.calculate_hotness_score(article)
            com_score = detector.calculate_commercial_score(article)
            total_score = hot_score * 0.6 + com_score * 0.4
            
            # 抽取关键词 & 搜索模板
            keywords = detector.extract_search_keywords(article)
            youtube_search = f"{keywords[0]} review" if keywords else article.get('title','')
            tiktok_search = f"#{keywords[0]} #unboxing" if keywords else article.get('title','')
            
            scored_articles.append((total_score, article, keywords, youtube_search, tiktok_search, hot_score, com_score))
        
        # 排序并按总分从高到低
        scored_articles.sort(key=lambda x: x[0], reverse=True)

        # 3️⃣ 品牌多样性控制：限制大品牌出现频次
        big_brands = {'apple', 'tesla', 'sony', 'google', 'samsung', 'microsoft', 'meta'}
        brand_limit_big = 2   # 大品牌最多 2 条
        brand_limit_other = 5 # 其他品牌最多 5 条
        brand_counter: Dict[str, int] = {}
        selected_articles = []

        def get_primary_brand(article_title: str) -> str:
            t = article_title.lower()
            for bw in [
                'apple', 'tesla', 'sony', 'google', 'samsung', 'microsoft', 'meta',
                'amazon', 'nike', 'adidas', 'huawei', 'xiaomi', 'shein', 'aliexpress',
                'etsy', 'shopify', 'temu', 'dhl', 'fedex', 'ups']:
                if bw in t:
                    return bw
            return 'misc'

        for entry in scored_articles:
            if len(selected_articles) >= 100:
                break
            total, article, keywords, y_search, t_search, h_score, c_score = entry
            brand = get_primary_brand(article.get('title', ''))
            cnt = brand_counter.get(brand, 0)
            limit = brand_limit_big if brand in big_brands else brand_limit_other
            if cnt >= limit:
                continue
            brand_counter[brand] = cnt + 1
            selected_articles.append(entry)

        top_articles = selected_articles
        
        print(f"\n🔥 TOP 100 最热门且适合带货的新闻标题(已去重大品牌):")
        print("=" * 80)
        
        titles_output = []
        for i, (total, article, keywords, y_search, t_search, h_score, c_score) in enumerate(top_articles, 1):
            title = article.get('title', 'No title')
            source = article.get('source', {}).get('name', 'Unknown')
            print(f"{i:3d}. {title}")
            titles_output.append({
                'title': title,
                'source': source,
                'hotness_score': round(h_score,1),
                'commercial_score': round(c_score,1),
                'total_score': round(total,1),
                'keywords': keywords,
                'youtube_search': y_search,
                'tiktok_search': t_search,
                'url': article.get('url',''),
                'publishedAt': article.get('publishedAt','')
            })
        
        # 保存标题列表
        output = {
            'timestamp': now.isoformat(),
            'total_articles_analyzed': len(news_collection),
            'filtered_articles_count': len(filtered_articles),
            'description': '基于热门度+商业度的TOP100新闻标题及搜索关键词',
            'top_100_hot_commercial_news': titles_output
        }
        
        with open('hot_commercial_news_titles.json', 'w', encoding='utf-8') as f:
            json.dump(output, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 结果已保存到 hot_commercial_news_titles.json")
        
    except Exception as error:
        print(f"❌ 获取失败: {error}")

def find_influencer_topics():
    """寻找适合带货达人的热门话题"""
    api_key = "6975e8c0e3904a499b90c2e8002b6052"
    
    detector = InfluencerTopicsDetector(api_key)
    
    try:
        print("🎯 正在寻找适合带货达人的热门话题...\n")
        
        result = detector.get_influencer_friendly_topics()
        
        if 'error' in result:
            print(f"❌ 分析失败: {result['error']}")
            return
        
        print("📈 分析完成！")
        print(f"📅 分析时间范围: {result['week_range']['from']} 到 {result['week_range']['to']}")
        print(f"📊 总文章数: {result['total_articles_analyzed']}")
        print(f"💼 商业文章数: {result['commercial_articles_found']}")
        print(f"🎯 带货话题数: {result['influencer_friendly_topics']}\n")
        
        print("🔥 最适合寻找带货达人的TOP10话题:")
        print("=" * 60)
        
        for index, topic in enumerate(result['top_topics_for_influencers'], 1):
            print(f"\n{index}. 【{topic['topic_title'].upper()}】")
            print(f"   💰 商业评分: {topic['commercial_score']:.1f}")
            print(f"   📰 相关文章: {topic['articles_count']} 篇")
            print(f"   🏷️ 商业类别: {', '.join(topic['commercial_categories'])}")
            
            # 达人搜索建议
            search_info = topic['influencer_search']
            print(f"   🔍 主要搜索词: {', '.join(search_info['primary_keywords'])}")
            print(f"   🎭 推荐达人类型: {', '.join(search_info['recommended_influencer_types'])}")
            
            # 平台策略
            platform = search_info['platform_strategy']
            focus_platform = platform['focus']
            if focus_platform == 'youtube':
                print(f"   📺 推荐平台: YouTube (更适合深度内容)")
            elif focus_platform == 'tiktok':
                print(f"   📱 推荐平台: TikTok (更适合快速展示)")
            else:
                print(f"   📺📱 推荐平台: YouTube + TikTok (双平台)")
            
            # 搜索模板
            templates = search_info['search_templates']
            print(f"   🔗 YouTube搜索: \"{templates['youtube']}\"")
            print(f"   🔗 TikTok搜索: \"{templates['tiktok']}\"")
            
            # 代表性文章标题
            print(f"   📖 热门文章:")
            for article in topic['articles'][:1]:
                print(f"      • {article.get('title', 'No title')}")
        
        # 保存结果到文件
        with open('influencer_topics.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2, default=str)
        print(f"\n💾 详细结果已保存到 influencer_topics.json")
        
    except Exception as error:
        print(f"❌ 分析失败: {error}")


if __name__ == "__main__":
    # 直接输出100个新闻标题
    get_commercial_news_titles()
    
    # 或者运行完整的话题分析
    # find_influencer_topics()